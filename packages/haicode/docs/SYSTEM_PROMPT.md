# HaiCodeAgent 系统提示词指南

## 概述

HaiCodeAgent 现在支持完整的系统提示词功能，参考了 packages/core 的提示词逻辑，并遵循 LangChain.js 和 LangGraph 的最佳实践。

## 功能特性

### 1. 默认系统提示词
- 基于 packages/core 的提示词逻辑
- 自动包含工具信息
- 包含用户环境信息（操作系统、工作目录等）
- 支持中文回复

### 2. 自定义系统提示词
- 支持完全自定义系统提示词
- 动态更新系统提示词
- 提示词缓存机制提高性能

### 3. 用户记忆功能
- 支持添加用户偏好和记忆
- 自动附加到系统提示词末尾
- 跨会话持久化用户信息

### 4. 工具信息集成
- 自动将可用工具信息添加到系统提示词
- 包含工具名称和描述
- 指导 LLM 正确使用工具

## 使用方法

### 基本使用

```typescript
import { HaiCodeAgent } from '@haiagent/core';

// 创建带有默认系统提示词的 Agent
const agent = new HaiCodeAgent({
  model: 'gpt-3.5-turbo',
  apiKey: 'your-api-key'
});

// 查看当前系统提示词
console.log(agent.getCurrentSystemPrompt());
```

### 自定义系统提示词

```typescript
// 设置自定义系统提示词
const customPrompt = `
你是一个专业的代码审查助手。你的任务是：
- 分析代码质量和最佳实践
- 提供具体的改进建议
- 识别潜在的bug和安全问题
- 始终以中文回复，保持专业和友好的语调
`;

agent.updateSystemPrompt(customPrompt);
```

### 添加用户记忆

```typescript
// 添加用户偏好和记忆
const userMemory = `
用户偏好：
- 喜欢使用 TypeScript
- 偏好函数式编程风格
- 使用 ESLint 和 Prettier 进行代码格式化
- 项目主要使用 React 和 Node.js
`;

agent.updateUserMemory(userMemory);
```

### 配置选项

```typescript
// 在创建时配置系统提示词和用户记忆
const agent = new HaiCodeAgent({
  model: 'gpt-4',
  apiKey: 'your-api-key',
  systemPrompt: 'Custom system prompt',
  userMemory: 'User preferences and memory',
  temperature: 0.7,
  maxTokens: 2000
});
```

### 管理系统提示词

```typescript
// 获取当前系统提示词
const currentPrompt = agent.getCurrentSystemPrompt();

// 重置为默认系统提示词
agent.resetSystemPrompt();

// 获取工具信息
const tools = agent.getToolsInfo();
console.log('可用工具:', tools);
```

## 最佳实践

### 1. 系统提示词设计
- **明确角色定义**：清楚地定义 AI 的角色和职责
- **具体指令**：提供具体、可操作的指令
- **示例和格式**：包含期望的输出格式和示例
- **约束条件**：明确说明限制和边界

### 2. 用户记忆管理
- **相关性**：只保存与任务相关的用户信息
- **简洁性**：保持记忆内容简洁明了
- **更新频率**：定期更新过时的用户偏好

### 3. 性能优化
- **缓存机制**：系统自动缓存提示词以提高性能
- **按需更新**：只在必要时更新系统提示词
- **内容长度**：控制系统提示词长度以节省 token

## 技术实现

### 消息准备流程
1. 检查是否已存在系统消息
2. 如果不存在，生成系统提示词
3. 添加工具信息到提示词
4. 附加用户记忆（如果有）
5. 创建 SystemMessage 并添加到消息序列开头

### 缓存机制
- 系统提示词在首次生成后会被缓存
- 更新系统提示词或用户记忆时会清除缓存
- 下次调用时重新生成并缓存新的提示词

### 类型安全
- 使用 TypeScript 确保类型安全
- 支持多种消息格式（对象和字符串）
- 兼容 LangChain.js 的消息类型系统

## 示例代码

查看 `examples/systemPromptExample.ts` 获取完整的使用示例。

## 测试

运行测试以验证系统提示词功能：

```bash
npm test src/test/systemPrompt.test.ts
```

## 与 packages/core 的对比

| 功能 | packages/core | packages/haiagent |
|------|---------------|-------------------|
| 基础提示词 | ✅ | ✅ |
| 工具信息集成 | ✅ | ✅ |
| 用户记忆 | ✅ | ✅ |
| 环境信息 | ✅ | ✅ |
| 动态更新 | ✅ | ✅ |
| 缓存机制 | ❌ | ✅ |
| LangGraph 集成 | ❌ | ✅ |

## 注意事项

1. **API 密钥**：确保设置有效的 OpenAI API 密钥
2. **Token 限制**：注意系统提示词长度对 token 使用的影响
3. **版本兼容性**：确保使用兼容的 LangChain.js 版本
4. **错误处理**：适当处理 API 调用可能出现的错误

## 贡献

欢迎提交 Issue 和 Pull Request 来改进系统提示词功能。
