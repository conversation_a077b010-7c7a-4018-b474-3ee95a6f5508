# HaiCode CLI

基于 LangChain.js 和 LangGraph 的 AI 编程助手命令行工具，支持流式输出和多种使用模式。

## 功能特性

- 🚀 **流式输出**: 实时显示 AI 响应，提供更好的用户体验
- 🛠️ **丰富的工具集**: 内置文件操作、shell 命令、搜索等工具
- 💬 **多种交互模式**: 支持交互式对话、单次问答和管道输入
- ⚙️ **灵活配置**: 支持自定义模型、API 端点等参数
- 🔧 **模块化设计**: 基于 LangChain.js 和 LangGraph 构建，易于扩展
- 🔌 **MCP 集成**: 支持 Model Context Protocol (MCP) 服务器，扩展工具能力

## 安装

```bash
npm install @ht/hai-code-cli -g
```

## 快速开始

### 使用

```bash
# 非交互模式
hai-code "Hello, how can you help me?"

# 交互模式
hai-code --interactive

# 指定模型（默认: ht::saas-deepseek-v3）
hai-code -m ht::saas-deepseek-v3 "Explain this code"

# 使用 OpenAI-Compatible 接口（例如本地/代理）
OPENAI_BASE_URL=http://localhost:11434/v1 OPENAI_API_KEY=ollama \
  hai-code -m ht::saas-deepseek-v3 "你好"
```

### 命令行参数

- `-m, --model <model>`: 指定使用的模型（默认: ht::saas-deepseek-v3）
- `-p, --prompt <prompt>`: 非交互模式下的提示词
- `-b, --base-url <url>`: LLM API 的基础 URL（也可使用 OPENAI_BASE_URL 环境变量）
- `-i, --interactive`: 启动交互模式
- `-d, --debug`: 启用调试模式
- `-h, --help`: 显示帮助信息
- `-v, --version`: 显示版本信息

### 环境变量

- `OPENAI_API_KEY`: OpenAI API 密钥
- `OPENAI_BASE_URL`: OpenAI 兼容的 API 基础 URL
- `HAI_CODE_MODEL`: 默认使用的模型

## 可用工具

Hai Code Agent 内置了以下工具：

- **文件操作**: 读取、写入、编辑文件
- **Shell 命令**: 执行系统命令
- **搜索功能**: 在文件中搜索内容(deprecated)
- **Glob 模式**: 文件路径匹配
- **内存工具**: 记忆和检索信息
- **MCP 工具**: 通过 MCP 服务器扩展的外部工具

## MCP 集成

HaiCodeAgent 支持 Model Context Protocol (MCP) 集成，允许使用外部 MCP 服务器提供的工具。

### 基本配置

```typescript
import { HaiCodeAgent } from '@ht/hai-code-cli';

const agent = new HaiCodeAgent({
  mcp: {
    enabled: true,
    servers: {
      "time-server": {
        command: "uvx",
        args: ["mcp-server-time"],
        transport: "stdio",
      },
    },
  },
});
```

### 预定义服务器

```typescript
import { PREDEFINED_MCP_SERVERS } from '@ht/hai-code-cli';

const agent = new HaiCodeAgent({
  mcp: {
    enabled: true,
    servers: {
      "time-server": PREDEFINED_MCP_SERVERS["time-server"],
      "browser-tools": PREDEFINED_MCP_SERVERS["browser-tools"],
    },
  },
});
```

## 开发

```bash
# 监听模式构建
npm run build:watch

# 运行测试
npm test

# 代码检查
npm run lint
```

## 注意事项

1. 确保已正确配置 API 密钥和端点
2. 某些工具可能需要特定的系统权限
3. 在生产环境中使用时，请注意 API 调用的成本和频率限制
