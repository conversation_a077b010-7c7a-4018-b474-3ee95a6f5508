import { tool } from "@langchain/core/tools";
import { z } from "zod";
import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

/**
 * Grep tool for langchain - searches for patterns in file contents
 */
export const grepTool = tool(
  async (params) => {
    const { pattern, path: searchPath, include } = params;
    
    try {
      // Determine search directory
      const searchDir = searchPath ? path.resolve(searchPath) : process.cwd();
      
      // Validate search directory
      if (!fs.existsSync(searchDir)) {
        throw new Error(`Search directory does not exist: ${searchDir}`);
      }
      
      if (!fs.statSync(searchDir).isDirectory()) {
        throw new Error(`Search path is not a directory: ${searchDir}`);
      }
      
      // Create regex pattern
      const regex = new RegExp(pattern, 'i'); // case-insensitive by default
      
      // Determine glob pattern for files to search
      const globPattern = include || '**/*';
      const fullGlobPattern = path.join(searchDir, globPattern);
      
      // Find files to search
      const files = await glob(fullGlobPattern, {
        nodir: true,
        ignore: ['**/node_modules/**', '**/.git/**', '**/dist/**', '**/build/**']
      });
      
      const matches: Array<{
        filePath: string;
        lineNumber: number;
        line: string;
      }> = [];
      
      // Search through each file
      for (const file of files) {
        try {
          // Skip binary files and very large files
          const stats = fs.statSync(file);
          if (stats.size > 10 * 1024 * 1024) { // Skip files larger than 10MB
            continue;
          }
          
          const content = fs.readFileSync(file, 'utf-8');
          const lines = content.split('\n');
          
          lines.forEach((line, index) => {
            if (regex.test(line)) {
              matches.push({
                filePath: path.relative(process.cwd(), file),
                lineNumber: index + 1,
                line: line.trim()
              });
            }
          });
        } catch (error) {
          // Skip files that can't be read (binary files, permission issues, etc.)
          continue;
        }
      }
      
      // Format results
      if (matches.length === 0) {
        return `No matches found for pattern "${pattern}" in ${path.relative(process.cwd(), searchDir)}`;
      }
      
      let result = `Found ${matches.length} matches for pattern "${pattern}":\n\n`;
      
      matches.forEach((match, index) => {
        result += `[${index + 1}] ${match.filePath}:${match.lineNumber}\n`;
        result += `    ${match.line}\n\n`;
      });
      
      return result;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to search file content: ${errorMessage}`);
    }
  },
  {
    name: "search_file_content",
    description: "Searches for a regular expression pattern within the content of files in a specified directory. Can filter files by a glob pattern. Returns the lines containing matches, along with their file paths and line numbers.",
    schema: z.object({
      pattern: z.string().describe("The regular expression (regex) pattern to search for within file contents (e.g., 'function\\s+myFunction', 'import\\s+\\{.*\\}\\s+from\\s+.*')."),
      path: z.string().optional().describe("Optional: The absolute path to the directory to search within. If omitted, searches the current working directory."),
      include: z.string().optional().describe("Optional: A glob pattern to filter which files are searched (e.g., '*.js', '*.{ts,tsx}', 'src/**'). If omitted, searches all files (respecting potential global ignores)."),
    }),
  }
);
