import { tool } from "@langchain/core/tools";
import { z } from "zod";
import fs from 'fs';
import path from 'path';

/**
 * Edit tool for langchain - replaces text within a file
 */
export const editTool = tool(
  async (params) => {
    const { file_path, old_string, new_string, expected_replacements } = params;
    
    try {
      // Validate file path
      if (!path.isAbsolute(file_path)) {
        throw new Error(`File path must be absolute, but was relative: ${file_path}. You must provide an absolute path.`);
      }
      
      // Check if file exists
      if (!fs.existsSync(file_path)) {
        throw new Error(`File not found: ${file_path}`);
      }
      
      // Check if it's a file (not directory)
      const stats = fs.statSync(file_path);
      if (!stats.isFile()) {
        throw new Error(`Path is not a file: ${file_path}`);
      }
      
      // Read current file content
      const currentContent = fs.readFileSync(file_path, 'utf-8');
      
      // Count occurrences of old_string
      const occurrences = (currentContent.match(new RegExp(escapeRegExp(old_string), 'g')) || []).length;
      
      if (occurrences === 0) {
        throw new Error(`String not found in file: "${old_string}"`);
      }
      
      // Check expected replacements
      const expectedCount = expected_replacements || 1;
      if (occurrences !== expectedCount) {
        throw new Error(`Expected ${expectedCount} occurrences of "${old_string}", but found ${occurrences}`);
      }
      
      // Perform replacement
      const newContent = currentContent.replace(new RegExp(escapeRegExp(old_string), 'g'), new_string);
      
      // Write updated content back to file
      fs.writeFileSync(file_path, newContent, 'utf-8');
      
      const relativePath = path.relative(process.cwd(), file_path);
      const oldLines = old_string.split('\n').length;
      const newLines = new_string.split('\n').length;
      const lineDiff = newLines - oldLines;
      
      let result = `Successfully replaced ${occurrences} occurrence(s) in file: ${relativePath}\n`;
      result += `Old text (${oldLines} lines): "${old_string.substring(0, 100)}${old_string.length > 100 ? '...' : ''}"\n`;
      result += `New text (${newLines} lines): "${new_string.substring(0, 100)}${new_string.length > 100 ? '...' : ''}"\n`;
      if (lineDiff !== 0) {
        result += `Line count change: ${lineDiff > 0 ? '+' : ''}${lineDiff}`;
      }
      
      return result;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to edit file: ${errorMessage}`);
    }
  },
  {
    name: "replace",
    description: "Replaces text within a file. By default, replaces a single occurrence, but can replace multiple occurrences when `expected_replacements` is specified. This tool requires providing significant context around the change to ensure precise targeting.",
    schema: z.object({
      file_path: z.string().describe("The absolute path to the file to modify. Must start with '/'."),
      old_string: z.string().describe("The exact literal text to replace (including all whitespace, indentation, newlines, and surrounding code etc.). Must uniquely identify the instance(s) to change."),
      new_string: z.string().describe("The exact literal text to replace `old_string` with (also including all whitespace, indentation, newlines, and surrounding code etc.). Ensure the resulting code is correct and idiomatic."),
      expected_replacements: z.number().optional().describe("Number of replacements expected. Defaults to 1 if not specified. Use when you want to replace multiple occurrences."),
    }),
  }
);

/**
 * Escapes special regex characters in a string
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
