import { tool } from "@langchain/core/tools";
import { z } from "zod";
import fs from 'fs';
import path from 'path';

/**
 * ReadFile tool for langchain - reads and returns the content of a specified file
 */
export const readFileTool = tool(
  async (params) => {
    const { absolute_path, offset, limit } = params;
    
    try {
      // Validate file path
      if (!path.isAbsolute(absolute_path)) {
        throw new Error(`File path must be absolute, but was relative: ${absolute_path}. You must provide an absolute path.`);
      }
      
      // Check if file exists
      if (!fs.existsSync(absolute_path)) {
        throw new Error(`File not found: ${absolute_path}`);
      }
      
      // Check if it's a file (not directory)
      const stats = fs.statSync(absolute_path);
      if (!stats.isFile()) {
        throw new Error(`Path is not a file: ${absolute_path}`);
      }
      
      // Read file content
      const content = fs.readFileSync(absolute_path, 'utf-8');
      const lines = content.split('\n');
      
      // Apply offset and limit if specified
      let resultLines = lines;
      if (offset !== undefined || limit !== undefined) {
        const startIndex = offset || 0;
        const endIndex = limit !== undefined ? startIndex + limit : lines.length;
        resultLines = lines.slice(startIndex, endIndex);
      }
      
      const resultContent = resultLines.join('\n');
      const relativePath = path.relative(process.cwd(), absolute_path);
      
      return `File: ${relativePath}\nContent:\n${resultContent}`;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to read file: ${errorMessage}`);
    }
  },
  {
    name: "read_file",
    description: "Reads and returns the content of a specified file from the local filesystem. Handles text files and can read specific line ranges.",
    schema: z.object({
      absolute_path: z.string().describe("The absolute path to the file to read (e.g., '/home/<USER>/project/file.txt'). Relative paths are not supported. You must provide an absolute path."),
      offset: z.number().optional().describe("Optional: For text files, the 0-based line number to start reading from. Requires 'limit' to be set. Use for paginating through large files."),
      limit: z.number().optional().describe("Optional: For text files, maximum number of lines to read. Use with 'offset' to paginate through large files. If omitted, reads the entire file (if feasible, up to a default limit)."),
    }),
  }
);
