/**
 * Langfuse 配置
 * 支持环境变量和默认配置
 *
 * 环境变量优先级：
 * - LANGFUSE_PUBLIC_KEY
 * - LANGFUSE_SECRET_KEY
 * - LANGFUSE_BASEURL
 * - LANGFUSE_ENABLED
 *
 * 测试环境 UI: http://eipsit.htsc.com.cn/htgy/talents-view/
 */

export interface LangfuseConfig {
  enabled: boolean;
  publicKey: string;
  secretKey: string;
  baseUrl: string;
  userId?: string;
  sessionId?: string;
  release?: string;
  version?: string;
  updateRoot?: boolean;
}

// 默认测试环境配置
const DEFAULT_CONFIG = {
  publicKey: 'pk-lf-658195da-ea24-4d87-a398-9f70f602a0c0',
  secretKey: 'sk-lf-8a7637d9-8a82-41cf-80a0-4aadf60204bb',
  baseUrl: 'http://************:8090/fst-observe-pipeline/connect/langfuse',
};

/**
 * 获取 Langfuse 配置
 * 优先使用环境变量，回退到默认配置
 */
export function getLangfuseConfig(): LangfuseConfig {
  const enabled = process.env.LANGFUSE_ENABLED !== 'false'; // 默认启用

  return {
    enabled,
    publicKey: process.env.LANGFUSE_PUBLIC_KEY || DEFAULT_CONFIG.publicKey,
    secretKey: process.env.LANGFUSE_SECRET_KEY || DEFAULT_CONFIG.secretKey,
    baseUrl: process.env.LANGFUSE_BASEURL || DEFAULT_CONFIG.baseUrl,
    release: process.env.LANGFUSE_RELEASE,
    version: process.env.LANGFUSE_VERSION,
    updateRoot: process.env.LANGFUSE_UPDATE_ROOT === 'true',
  };
}

// 向后兼容的导出
export const LANGFUSE_CONFIG = getLangfuseConfig();
