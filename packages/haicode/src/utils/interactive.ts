import { HaiCodeAgent } from '../index.js';
import chalk from 'chalk';

export async function runInteractiveMode(agent: HaiCodeAgent): Promise<void> {
  console.log(chalk.cyan('🤖 Hai Code Agent - Interactive mode'));
  console.log(chalk.gray('Type "exit" or "quit" to exit, Ctrl+C to quit immediately'));
  console.log('');

  // Use readline for interactive input
  const readline = await import('node:readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    prompt: chalk.blue('> '),
  });

  rl.prompt();

  rl.on('line', async (input) => {
    const trimmed = input.trim();

    if (trimmed === 'exit' || trimmed === 'quit') {
      rl.close();
      return;
    }

    if (trimmed === '') {
      rl.prompt();
      return;
    }

    try {
      console.log(''); // Add space before response
      
      for await (const chunk of agent.streamMessage(trimmed)) {
        process.stdout.write(chunk);
      }
      
      console.log('\n'); // Add newline after response
    } catch (error) {
      console.error(chalk.red('\nError:'), error instanceof Error ? error.message : String(error));
    }

    rl.prompt();
  });

  rl.on('close', async () => {
    console.log(chalk.gray('\nGoodbye! 👋'));
    await agent.flushLangfuse();
    process.exit(0);
  });

  // Handle Ctrl+C
  process.on('SIGINT', async () => {
    console.log(chalk.gray('\nGoodbye! 👋'));
    await agent.flushLangfuse();
    process.exit(0);
  });
}
